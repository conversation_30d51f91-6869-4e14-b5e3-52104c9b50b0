# 新闻联表查询实现

本项目实现了对 knNews 表的完整联表查询，包括 SEO 信息、内容信息和分类信息。

## 功能特性

### 主要查询功能
- 查询 knNews 主表的新闻基本信息
- 联表查询 knNewsSeo 表的 SEO 信息
- 联表查询 knNewsContent 表的内容信息
- 联表查询 knNewsClassify 表的分类信息
- 联表查询 knNewsLabel 表的标签信息

### 数据表关系
1. **knNews** (主表)
   - 主键: `id`
   - 分类关联字段: `newsClassifys` (存储分类ID字符串，如 "1,2,3")

2. **knNewsSeo** (SEO信息表)
   - 关联字段: `relationId` -> knNews.`id`
   - 包含: SEO标题、关键词、描述等

3. **knNewsContent** (内容表)
   - 关联字段: `newsId` -> knNews.`id`
   - 包含: 新闻正文内容

4. **knNewsClassify** (分类表)
   - 通过 knNews.`newsClassifys` 字段关联
   - 包含: 分类名称、唯一标识、状态等

5. **knNewsLabel** (标签表)
   - 通过 knNews.`newsLabels` 字段关联
   - 包含: 标签名称、唯一标识、排序、状态等

## 核心函数

### `getNewsWithDetails(newsId: string)`
根据新闻ID查询完整的新闻详情信息。

**参数:**
- `newsId`: 新闻ID字符串

**返回值:**
- `NewsWithDetails | null`: 新闻详情对象或null

### `getNewsClassifyDetails(classifyIds: string)`
根据分类ID字符串获取分类详情信息。

**参数:**
- `classifyIds`: 分类ID字符串，格式如 "1,2,3"

**返回值:**
- `NewsClassifyInfo[]`: 分类信息数组

### `getNewsLabelDetails(labelIds: string)`
根据标签ID字符串获取标签详情信息。

**参数:**
- `labelIds`: 标签ID字符串，格式如 "1,2,3"

**返回值:**
- `NewsLabelInfo[]`: 标签信息数组

### `printNewsDetails(news: NewsWithDetails)`
格式化打印新闻详情信息。

## 使用示例

```typescript
import { getNewsWithDetails, printNewsDetails } from './db/index';

async function example() {
  // 查询指定新闻的完整信息
  const news = await getNewsWithDetails('1128113088472006656');
  
  if (news) {
    // 打印格式化的新闻详情
    printNewsDetails(news);
    
    // 或者直接使用返回的数据
    console.log('新闻标题:', news.title);
    console.log('分类数量:', news.classifyInfo.length);
    console.log('标签数量:', news.labelInfo.length);
    console.log('是否有SEO信息:', news.seoInfo ? '是' : '否');
  }
}
```

## 查询结果示例

查询 ID 为 `1128113088472006656` 的新闻返回结果：

```json
{
  "id": "1128113088472006656",
  "title": "护士考试11",
  "introduction": "11112",
  "publishTime": "2025-09-17 10:05:30",
  "viewCount": 1,
  "newsClassifys": "1000577610924949504,976011872436154368",
  "newsLabels": null,
  "seoInfo": null,
  "contentInfo": {
    "id": "1128455325506760704",
    "content": "<p>111</p>",
    "createdAt": "2025-09-18 08:45:26"
  },
  "classifyInfo": [
    {
      "id": "1000577610924949504",
      "classifyName": "王卫帅问问",
      "uniqueIdentification": "wwsww"
    },
    {
      "id": "976011872436154368", 
      "classifyName": "资讯分类",
      "uniqueIdentification": "zxflcs-1"
    }
  ],
  "labelInfo": [
    {
      "id": "919444677910986752",
      "labelName": "热点资讯",
      "uniqueIdentification": "zo",
      "sorting": 20,
      "status": 1
    }
  ]
}
```

## 运行测试

```bash
# 运行主查询脚本
npx tsx db/index.ts

# 运行测试脚本
npx tsx db/test-query.ts
```

## 技术实现

- 使用 Drizzle ORM 进行类型安全的数据库查询
- 采用 `leftJoin` 进行联表查询，确保主表数据完整性
- 对于一对多关系（分类、标签），采用单独查询后组合的方式
- 包含完整的空值检查和错误处理
- TypeScript 类型定义确保代码安全性

## 性能优化

- **条件查询**: 只有当 newsClassifys/newsLabels 不为空时才执行查询
- **并行查询**: 使用 Promise.all 同时执行分类和标签查询
- **批量查询**: 使用 SQL IN 语句进行多ID查询，避免循环单个查询
- **早期返回**: 空字符串时直接返回空数组，避免无效查询

## 注意事项

1. 所有查询都包含 `deletedAt = 1` 的条件，确保只查询未删除的记录
2. 分类和标签查询使用 SQL IN 语句，支持多个ID的批量查询
3. 返回的数据结构完整，包含所有相关联的表信息
4. 错误处理完善，查询失败时会记录错误日志
5. 性能优化：只有当相关字段不为空时才执行对应的查询
