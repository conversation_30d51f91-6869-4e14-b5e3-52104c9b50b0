import { db<PERSON>anager, getCoreNewsDB, getUserDB, getLogDB, getDB } from './multi-db';
import { knNews, knNewsClassify } from './schema';
import { eq, and } from 'drizzle-orm';

/**
 * 多数据库使用示例
 */
export class MultiDatabaseExample {
  
  /**
   * 示例1：使用不同的数据库连接
   */
  async example1_BasicUsage() {
    console.log('=== 示例1：基本多数据库使用 ===');
    
    // 方式1：使用预定义的连接函数
    const coreNewsDB = getCoreNewsDB();
    const userDB = getUserDB();
    const logDB = getLogDB();
    
    // 方式2：使用通用连接函数
    const coreNewsDB2 = getDB('core_news');
    const userDB2 = getDB('user_db');
    
    console.log('数据库连接已建立');
    
    // 在核心新闻数据库中查询新闻
    try {
      const news = await coreNewsDB.select()
        .from(knNews)
        .where(eq(knNews.isPublish, 1))
        .limit(5);
      
      console.log(`从核心新闻数据库查询到 ${news.length} 条新闻`);
    } catch (error) {
      console.error('查询核心新闻数据库失败:', error);
    }
  }

  /**
   * 示例2：跨数据库事务处理（注意：MySQL不支持跨数据库事务）
   */
  async example2_CrossDatabaseOperations() {
    console.log('=== 示例2：跨数据库操作 ===');
    
    const coreNewsDB = getCoreNewsDB();
    const logDB = getLogDB();
    
    try {
      // 从核心数据库查询新闻
      const news = await coreNewsDB.select()
        .from(knNews)
        .where(eq(knNews.isPublish, 1))
        .limit(1);
      
      if (news.length > 0) {
        console.log(`查询到新闻: ${news[0].title}`);
        
        // 记录操作日志到日志数据库
        // 注意：这里需要在日志数据库中有相应的表结构
        console.log('操作日志已记录到日志数据库');
      }
    } catch (error) {
      console.error('跨数据库操作失败:', error);
    }
  }

  /**
   * 示例3：数据库连接测试
   */
  async example3_ConnectionTest() {
    console.log('=== 示例3：数据库连接测试 ===');
    
    const databases = dbManager.getAvailableDatabases();
    
    for (const dbName of databases) {
      const isConnected = await dbManager.testConnection(dbName);
      console.log(`数据库 '${dbName}': ${isConnected ? '连接成功' : '连接失败'}`);
    }
  }

  /**
   * 示例4：分库查询（根据业务逻辑选择不同数据库）
   */
  async example4_ShardingQuery(userId: string) {
    console.log('=== 示例4：分库查询示例 ===');
    
    // 根据用户ID的哈希值决定使用哪个数据库
    const dbName = this.getShardDatabase(userId);
    const db = getDB(dbName);
    
    console.log(`用户 ${userId} 的数据将从数据库 '${dbName}' 查询`);
    
    // 这里可以执行具体的查询操作
    // const userData = await db.select().from(userTable).where(eq(userTable.id, userId));
  }

  /**
   * 根据用户ID计算分片数据库
   * @param userId 用户ID
   * @returns 数据库名称
   */
  private getShardDatabase(userId: string): keyof typeof import('../drizzle.config').dbConfigs {
    // 简单的哈希分片逻辑
    const hash = this.simpleHash(userId);
    const databases = dbManager.getAvailableDatabases();
    const index = hash % databases.length;
    return databases[index];
  }

  /**
   * 简单哈希函数
   * @param str 输入字符串
   * @returns 哈希值
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 示例5：批量操作不同数据库
   */
  async example5_BatchOperations() {
    console.log('=== 示例5：批量操作不同数据库 ===');
    
    const operations = [
      { db: 'core_news', operation: 'query_news' },
      { db: 'user_db', operation: 'query_users' },
      { db: 'log_db', operation: 'insert_log' },
    ];

    const results = await Promise.allSettled(
      operations.map(async (op) => {
        const db = getDB(op.db as keyof typeof import('../drizzle.config').dbConfigs);
        
        switch (op.operation) {
          case 'query_news':
            return await db.select().from(knNews).limit(1);
          case 'query_users':
            // 这里需要用户表的schema
            console.log('查询用户数据（需要用户表schema）');
            return [];
          case 'insert_log':
            // 这里需要日志表的schema
            console.log('插入日志数据（需要日志表schema）');
            return [];
          default:
            throw new Error(`未知操作: ${op.operation}`);
        }
      })
    );

    results.forEach((result, index) => {
      const op = operations[index];
      if (result.status === 'fulfilled') {
        console.log(`数据库 '${op.db}' 操作 '${op.operation}' 成功`);
      } else {
        console.error(`数据库 '${op.db}' 操作 '${op.operation}' 失败:`, result.reason);
      }
    });
  }

  /**
   * 运行所有示例
   */
  async runAllExamples() {
    try {
      await this.example1_BasicUsage();
      await this.example2_CrossDatabaseOperations();
      await this.example3_ConnectionTest();
      await this.example4_ShardingQuery('user123');
      await this.example5_BatchOperations();
    } catch (error) {
      console.error('运行示例时出错:', error);
    }
  }
}

// 如果直接运行此文件，则执行示例
async function main() {
  const example = new MultiDatabaseExample();
  try {
    await example.runAllExamples();
    console.log('所有示例执行完成');
    process.exit(0);
  } catch (error) {
    console.error('执行示例失败:', error);
    process.exit(1);
  }
}

// 检查是否直接运行此文件
if (process.argv[1] && process.argv[1].includes('multi-db-example')) {
  main();
}
