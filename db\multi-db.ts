import 'dotenv/config';
import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2/promise";
import { dbConfigs } from '../drizzle.config';
import * as schema from "./schema";

// 数据库连接池管理器
class DatabaseManager {
  private pools: Map<string, mysql.Pool> = new Map();
  private connections: Map<string, any> = new Map();

  /**
   * 获取数据库连接池
   * @param dbName 数据库名称
   * @returns 数据库连接池
   */
  private getPool(dbName: keyof typeof dbConfigs): mysql.Pool {
    if (!this.pools.has(dbName)) {
      const config = dbConfigs[dbName];
      if (!config) {
        throw new Error(`数据库配置 '${dbName}' 不存在`);
      }
      
      const pool = mysql.createPool({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        // 连接池配置
        connectionLimit: 10,
        queueLimit: 0,
        // acquireTimeout: 60000,  // 移除无效配置
        // timeout: 60000,         // 移除无效配置
        // reconnect: true,        // 移除无效配置
      });
      
      this.pools.set(dbName, pool);
    }
    
    return this.pools.get(dbName)!;
  }

  /**
   * 获取 Drizzle 数据库连接
   * @param dbName 数据库名称
   * @returns Drizzle 数据库连接实例
   */
  getConnection(dbName: keyof typeof dbConfigs) {
    if (!this.connections.has(dbName)) {
      const pool = this.getPool(dbName);
      const connection = drizzle({ 
        client: pool, 
        schema,
        mode: 'default' 
      });
      this.connections.set(dbName, connection);
    }
    
    return this.connections.get(dbName)!;
  }

  /**
   * 关闭所有数据库连接
   */
  async closeAll(): Promise<void> {
    const closePromises: Promise<void>[] = [];
    
    for (const pool of this.pools.values()) {
      closePromises.push(pool.end());
    }
    
    await Promise.all(closePromises);
    this.pools.clear();
    this.connections.clear();
  }

  /**
   * 关闭指定数据库连接
   * @param dbName 数据库名称
   */
  async close(dbName: keyof typeof dbConfigs): Promise<void> {
    const pool = this.pools.get(dbName);
    if (pool) {
      await pool.end();
      this.pools.delete(dbName);
      this.connections.delete(dbName);
    }
  }

  /**
   * 测试数据库连接
   * @param dbName 数据库名称
   * @returns 连接是否成功
   */
  async testConnection(dbName: keyof typeof dbConfigs): Promise<boolean> {
    try {
      const pool = this.getPool(dbName);
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      console.error(`数据库 '${dbName}' 连接测试失败:`, error);
      return false;
    }
  }

  /**
   * 获取所有可用的数据库名称
   * @returns 数据库名称数组
   */
  getAvailableDatabases(): (keyof typeof dbConfigs)[] {
    return Object.keys(dbConfigs) as (keyof typeof dbConfigs)[];
  }
}

// 创建全局数据库管理器实例
export const dbManager = new DatabaseManager();

// 便捷的数据库连接获取函数
export const getCoreNewsDB = () => dbManager.getConnection('core_news');
export const getUserDB = () => dbManager.getConnection('user_db');
export const getLogDB = () => dbManager.getConnection('log_db');

// 通用的数据库连接获取函数
export const getDB = (dbName: keyof typeof dbConfigs) => dbManager.getConnection(dbName);

// 向后兼容：保持原有的 db 导出
export const db = getCoreNewsDB();

// 导出数据库配置
export { dbConfigs };

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('正在关闭数据库连接...');
  await dbManager.closeAll();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('正在关闭数据库连接...');
  await dbManager.closeAll();
  process.exit(0);
});
