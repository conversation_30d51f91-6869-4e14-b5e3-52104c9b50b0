/**
 * 多数据库 Schema 管理
 * 
 * 这个文件用于管理不同数据库的 schema 定义
 * 可以根据实际的分库分表需求来组织不同的 schema
 */

// 导入核心新闻相关的 schema
export * from '../schema';

// 用户数据库 schema（示例）
// 如果您有用户相关的表，可以在这里定义
// export * from './user-schema';

// 日志数据库 schema（示例）
// 如果您有日志相关的表，可以在这里定义
// export * from './log-schema';

// 其他数据库 schema
// 根据您的分库分表架构，可以继续添加更多的 schema 文件

/**
 * Schema 映射配置
 * 定义每个数据库应该使用哪些 schema
 */
export const schemaMapping = {
  core_news: {
    // 核心新闻数据库包含的表
    tables: [
      'kn_news',
      'kn_news_classify', 
      'kn_news_content',
      'kn_news_seo',
      'kn_news_label',
      'kn_classify_labels'
    ]
  },
  user_db: {
    // 用户数据库包含的表（示例）
    tables: [
      // 'users',
      // 'user_profiles',
      // 'user_sessions'
    ]
  },
  log_db: {
    // 日志数据库包含的表（示例）
    tables: [
      // 'access_logs',
      // 'error_logs',
      // 'operation_logs'
    ]
  }
};

/**
 * 获取指定数据库的表列表
 * @param dbName 数据库名称
 * @returns 表名数组
 */
export function getTablesForDatabase(dbName: keyof typeof schemaMapping): string[] {
  return schemaMapping[dbName]?.tables || [];
}

/**
 * 检查表是否属于指定数据库
 * @param tableName 表名
 * @param dbName 数据库名称
 * @returns 是否属于该数据库
 */
export function isTableInDatabase(tableName: string, dbName: keyof typeof schemaMapping): boolean {
  return getTablesForDatabase(dbName).includes(tableName);
}
