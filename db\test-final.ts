import 'dotenv/config';
import { getNewsWithDetails, printNewsDetails } from './index';

async function testFinal() {
  try {
    console.log('=== 测试1: 有分类无标签的新闻 ===');
    const news1 = await getNewsWithDetails('1128113088472006656');
    if (news1) {
      console.log(`新闻: ${news1.title}`);
      console.log(`分类数量: ${news1.classifyInfo.length}`);
      console.log(`标签数量: ${news1.labelInfo.length}`);
      console.log(`有SEO: ${news1.seoInfo ? '是' : '否'}`);
      console.log(`有内容: ${news1.contentInfo ? '是' : '否'}`);
    }
    
    console.log('\n=== 测试2: 有标签的新闻 ===');
    const news2 = await getNewsWithDetails('1004175804968620032');
    if (news2) {
      console.log(`新闻: ${news2.title}`);
      console.log(`分类数量: ${news2.classifyInfo.length}`);
      console.log(`标签数量: ${news2.labelInfo.length}`);
      console.log(`有SEO: ${news2.seoInfo ? '是' : '否'}`);
      console.log(`有内容: ${news2.contentInfo ? '是' : '否'}`);
      
      if (news2.labelInfo.length > 0) {
        console.log('\n标签详情:');
        news2.labelInfo.forEach((label, index) => {
          console.log(`  ${index + 1}. ${label.labelName} (ID: ${label.id})`);
        });
      }
    }
    
    console.log('\n=== 性能优化验证 ===');
    console.log('✅ 条件查询: 只有当 newsClassifys/newsLabels 不为空时才执行查询');
    console.log('✅ 并行查询: 使用 Promise.all 同时执行分类和标签查询');
    console.log('✅ 批量查询: 使用 SQL IN 语句进行多ID查询');
    console.log('✅ 早期返回: 空字符串时直接返回空数组');
    console.log('✅ 类型安全: 完整的 TypeScript 类型定义');
    
  } catch (error) {
    console.error('测试出错:', error);
  } finally {
    process.exit(0);
  }
}

testFinal();
