import 'dotenv/config';
import { getNewsWithDetails, printNewsDetails } from './index';

async function testQuery() {
  try {
    const newsId = '1128113088472006656';
    console.log(`开始查询新闻 ID: ${newsId}`);
    console.log('包含联表查询：knNewsSeo（SEO信息）、knNewsContent（内容信息）、knNewsClassify（分类信息）');

    // 使用封装的函数查询新闻详情
    const news = await getNewsWithDetails(newsId);

    if (!news) {
      console.log('未找到指定 ID 的新闻记录');
      return;
    }

    console.log('\n=== 查询成功 ===');
    console.log('查询到新闻:', news.title);
    console.log('包含分类数量:', news.classifyInfo.length);
    console.log('是否有SEO信息:', news.seoInfo ? '是' : '否');
    console.log('是否有内容信息:', news.contentInfo ? '是' : '否');

    // 打印完整的查询结果（JSON格式）
    console.log('\n=== 完整查询结果 (JSON) ===');
    console.log(JSON.stringify(news, null, 2));

    // 打印格式化的新闻详情
    printNewsDetails(news);

  } catch (error) {
    console.error('查询出错:', error);
  } finally {
    process.exit(0);
  }
}

testQuery();
