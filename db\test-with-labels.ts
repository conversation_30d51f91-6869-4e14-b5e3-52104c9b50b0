import 'dotenv/config';
import { sql, eq, and, isNotNull } from 'drizzle-orm';
import { knNews } from "./schema";
import { db } from './utils';
import { getNewsWithDetails, printNewsDetails } from './index';

async function findNewsWithLabels() {
  try {
    console.log('查找有标签的新闻...');
    
    // 查找有标签的新闻
    const newsWithLabels = await db.select({
      id: knNews.id,
      title: knNews.title,
      newsLabels: knNews.newsLabels,
    })
    .from(knNews)
    .where(
      and(
        isNotNull(knNews.newsLabels),
        sql`${knNews.newsLabels} != ''`,
        eq(knNews.deletedAt, 1)
      )
    )
    .limit(5);

    console.log(`找到 ${newsWithLabels.length} 条有标签的新闻:`);
    
    if (newsWithLabels.length > 0) {
      newsWithLabels.forEach((news, index) => {
        console.log(`${index + 1}. ID: ${news.id}, 标题: ${news.title}, 标签: ${news.newsLabels}`);
      });
      
      // 测试第一个有标签的新闻
      const firstNews = newsWithLabels[0];
      console.log(`\n=== 测试查询新闻: ${firstNews.title} (ID: ${firstNews.id}) ===`);
      
      const newsDetails = await getNewsWithDetails(firstNews.id);
      
      if (newsDetails) {
        console.log('\n=== 完整查询结果 (JSON) ===');
        console.log(JSON.stringify(newsDetails, null, 2));
        
        printNewsDetails(newsDetails);
      } else {
        console.log('未找到新闻详情');
      }
    } else {
      console.log('没有找到有标签的新闻，使用默认新闻进行测试');
      
      // 如果没有找到有标签的新闻，使用默认新闻
      const newsDetails = await getNewsWithDetails('1128113088472006656');
      if (newsDetails) {
        printNewsDetails(newsDetails);
      }
    }
    
  } catch (error) {
    console.error('查询出错:', error);
  } finally {
    process.exit(0);
  }
}

findNewsWithLabels();
