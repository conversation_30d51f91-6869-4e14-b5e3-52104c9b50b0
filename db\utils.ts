import 'dotenv/config';
import { sql, eq, isNull, and, desc, count, like } from 'drizzle-orm';
import { alias } from 'drizzle-orm/mysql-core';
import { knNews, knNewsClassify, knNewsContent, knNewsSeo } from "./schema";

// 使用新的多数据库管理器
import { db, dbManager, getCoreNewsDB, getUserDB, getLogDB, getDB } from './multi-db';

// 向后兼容：保持原有的导出
export { db };

// 导出多数据库相关功能
export {
  dbManager,
  getCoreNewsDB,
  getUserDB,
  getLogDB,
  getDB
};