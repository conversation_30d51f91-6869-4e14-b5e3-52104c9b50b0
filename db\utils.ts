import 'dotenv/config';
import { sql, eq, isNull, and, desc, count, like } from 'drizzle-orm';
import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2/promise";
import { knNews, knNewsClassify, knNewsContent, knNewsSeo } from "./schema";
import { alias } from 'drizzle-orm/mysql-core';

const poolConnection = mysql.createPool({
  // host: "**************",
  // port: 3308,
  // user: "root",
  // password: '<PERSON><PERSON><PERSON>@2021',
  // database: "kuke_core_news",
  // host: "127.0.0.1",
  // port: 3307,
  // user: "root",
  // password:'root',
  // database: "kuke_core_news",
  // debug: true,
  host: "kk-test.rwlb.rds.aliyuncs.com", 
    port: 3306,
    user: "kuketest",
    password:'Abcd!234',
    database: "kuke_core_news",
});
export const db = drizzle({ client: poolConnection, schema: { knNews, knNewsClassify, knNewsContent, knNewsSeo }, mode: 'default' });