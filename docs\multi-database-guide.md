# Drizzle ORM 多数据库配置指南

本指南将帮助您在 Drizzle ORM 项目中配置和使用多数据库连接，以支持分库分表架构。

## 📋 目录

- [配置概述](#配置概述)
- [文件结构](#文件结构)
- [基本使用](#基本使用)
- [迁移管理](#迁移管理)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🔧 配置概述

### 当前架构

项目现在支持多数据库连接，主要包含以下组件：

1. **drizzle.config.ts** - 多数据库配置文件
2. **db/multi-db.ts** - 数据库连接管理器
3. **db/utils.ts** - 向后兼容的工具函数
4. **scripts/multi-db-migrate.ts** - 多数据库迁移脚本

### 数据库配置

在 `drizzle.config.ts` 中定义了多个数据库配置：

```typescript
export const dbConfigs = {
  core_news: {
    host: "kk-test.rwlb.rds.aliyuncs.com",
    port: 3306,
    user: "kuketest", 
    password: 'Abcd!234',
    database: "kuke_core_news",
  },
  user_db: {
    // 用户数据库配置
  },
  log_db: {
    // 日志数据库配置
  }
};
```

## 📁 文件结构

```
project/
├── drizzle.config.ts          # 多数据库配置
├── db/
│   ├── multi-db.ts            # 数据库连接管理器
│   ├── utils.ts               # 向后兼容工具
│   ├── schema.ts              # 数据库 Schema
│   ├── multi-db-example.ts    # 使用示例
│   └── schemas/
│       └── index.ts           # Schema 映射管理
├── scripts/
│   └── multi-db-migrate.ts    # 迁移脚本
└── docs/
    └── multi-database-guide.md # 本文档
```

## 🚀 基本使用

### 1. 安装依赖

```bash
npm install tsx
```

### 2. 获取数据库连接

```typescript
import { getCoreNewsDB, getUserDB, getLogDB, getDB } from './db/multi-db';

// 方式1：使用预定义的连接函数
const coreNewsDB = getCoreNewsDB();
const userDB = getUserDB();
const logDB = getLogDB();

// 方式2：使用通用连接函数
const coreNewsDB2 = getDB('core_news');
const userDB2 = getDB('user_db');
```

### 3. 执行数据库查询

```typescript
import { knNews } from './db/schema';
import { eq } from 'drizzle-orm';

// 查询核心新闻数据库
const news = await coreNewsDB.select()
  .from(knNews)
  .where(eq(knNews.isPublish, 1))
  .limit(10);
```

### 4. 向后兼容

现有代码无需修改，原有的 `db` 导出仍然可用：

```typescript
import { db } from './db/utils';

// 这仍然指向核心新闻数据库
const news = await db.select().from(knNews).limit(10);
```

## 🔄 迁移管理

### 可用命令

```bash
# 为指定数据库生成迁移文件
npm run db:multi generate --database=core_news

# 为所有数据库生成迁移文件
npm run db:multi generate

# 执行指定数据库的迁移
npm run db:multi migrate --database=core_news

# 推送 schema 到指定数据库
npm run db:multi push --database=core_news

# 推送到所有数据库
npm run db:multi push

# 检查 schema 差异
npm run db:multi check --database=core_news

# 从数据库拉取 schema
npm run db:multi pull --database=core_news

# 显示帮助信息
npm run db:multi --help
```

### 传统命令（仍然可用）

```bash
# 这些命令仍然针对默认的核心新闻数据库
npm run db:generate
npm run db:migrate
npm run db:push
```

## 🧪 测试连接

### 测试所有数据库连接

```bash
npm run db:test
```

### 测试单个数据库连接

```bash
npm run db:test-connection
```

### 手动测试

```typescript
import { dbManager } from './db/multi-db';

// 测试所有数据库连接
const databases = dbManager.getAvailableDatabases();
for (const dbName of databases) {
  const isConnected = await dbManager.testConnection(dbName);
  console.log(`${dbName}: ${isConnected ? '连接成功' : '连接失败'}`);
}
```

## 💡 最佳实践

### 1. 数据库选择策略

```typescript
// 根据业务逻辑选择数据库
function getShardDatabase(userId: string): string {
  const hash = simpleHash(userId);
  const databases = ['user_db_1', 'user_db_2', 'user_db_3'];
  return databases[hash % databases.length];
}

// 使用示例
const userDb = getDB(getShardDatabase(userId));
```

### 2. 连接池管理

```typescript
// 在应用关闭时优雅地关闭所有连接
process.on('SIGINT', async () => {
  await dbManager.closeAll();
  process.exit(0);
});
```

### 3. 错误处理

```typescript
try {
  const result = await coreNewsDB.select().from(knNews);
} catch (error) {
  console.error('数据库查询失败:', error);
  // 实现重试逻辑或降级处理
}
```

### 4. 批量操作

```typescript
// 并行操作多个数据库
const operations = [
  getCoreNewsDB().select().from(knNews).limit(10),
  getUserDB().select().from(userTable).limit(10),
  getLogDB().select().from(logTable).limit(10),
];

const results = await Promise.allSettled(operations);
```

## 🔧 配置自定义数据库

### 1. 添加新数据库配置

在 `drizzle.config.ts` 中添加新的数据库配置：

```typescript
export const dbConfigs = {
  // 现有配置...
  new_db: {
    host: "your-host",
    port: 3306,
    user: "your-user",
    password: 'your-password',
    database: "your_database",
  },
};
```

### 2. 更新 Schema 映射

在 `db/schemas/index.ts` 中添加新数据库的表映射：

```typescript
export const schemaMapping = {
  // 现有映射...
  new_db: {
    tables: [
      'your_table_1',
      'your_table_2',
    ]
  }
};
```

### 3. 添加便捷函数

在 `db/multi-db.ts` 中添加新的便捷函数：

```typescript
export const getNewDB = () => dbManager.getConnection('new_db');
```

## 🐛 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务是否正常运行

2. **权限错误**
   - 验证用户名和密码
   - 检查数据库用户权限
   - 确认数据库是否存在

3. **Schema 不匹配**
   - 运行 `npm run db:multi check` 检查差异
   - 使用 `npm run db:multi generate` 生成新的迁移文件

### 调试技巧

```typescript
// 启用调试模式
const pool = mysql.createPool({
  // 其他配置...
  debug: true,
});

// 监听连接事件
pool.on('connection', (connection) => {
  console.log('新连接建立:', connection.threadId);
});

pool.on('error', (err) => {
  console.error('连接池错误:', err);
});
```

## 📚 相关资源

- [Drizzle ORM 官方文档](https://orm.drizzle.team/)
- [MySQL2 文档](https://github.com/sidorares/node-mysql2)
- [项目示例代码](./db/multi-db-example.ts)

---

如果您在使用过程中遇到问题，请查看示例代码或联系开发团队获取支持。
