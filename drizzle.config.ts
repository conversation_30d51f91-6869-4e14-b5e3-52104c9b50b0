// import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

// 多数据库配置
export const dbConfigs = {
  // 核心新闻数据库
  core_news: {
    host: "kk-test.rwlb.rds.aliyuncs.com",
    port: 3306,
    user: "kuketest",
    password: 'Abcd!234',
    database: "kuke_core_news",
  },
  // 用户数据库（示例）
  user_db: {
    host: "kk-test.rwlb.rds.aliyuncs.com",
    port: 3306,
    user: "kuketest",
    password: 'Abcd!234',
    database: "kuke_user_db",
  },
  // 日志数据库（示例）
  log_db: {
    host: "kk-test.rwlb.rds.aliyuncs.com",
    port: 3306,
    user: "kuketest",
    password: 'Abcd!234',
    database: "kuke_log_db",
  },
  // 可以根据需要添加更多数据库配置
};

// 默认配置（用于 drizzle-kit 命令）
export default defineConfig({
  out: './db/drizzle',
  schema: './db/schema.ts',
  dialect: 'mysql',
  dbCredentials: dbConfigs.core_news,
  tablesFilter: [
    "kn_news",
    'kn_news_classify',
    'kn_news_content',
    'kn_classify_labels',
    'kn_news_seo',
    'kn_news_label',
  ],
});