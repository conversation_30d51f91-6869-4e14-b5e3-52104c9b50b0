// import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';
export default defineConfig({
  out: './db/drizzle',
  schema: './db/schema.ts',
  dialect: 'mysql',
  dbCredentials: {
    // 旧测试环境
    // host: "**************",
    // port: 3308,
    // user: "root",
    // password:'<PERSON><PERSON><PERSON>@2021',
    // database: "kuke_core_news",
    // ----- 新测试环境
    // user: "root",
    // password:'<PERSON><PERSON><PERSON>@2021',
    // pc-2zeem770fck71s49r.rwlb.rds.aliyuncs.com:3306 (内网地址)
    host: "kk-test.rwlb.rds.aliyuncs.com", 
    port: 3306,
    user: "kuketest",
    password:'Abcd!234',
    database: "kuke_core_news",
    // -----
    // url: process.env.DATABASE_URL!,
    // host: "127.0.0.1",
    // port: 3307,
    // user: "root",
    // password:'root',
    // database: "kuke_core_news",
  },
  // extensionsFilters: ["postgis"],
  // schemaFilter: ["public"],
  tablesFilter: [
    "kn_news",
    'kn_news_classify',
    'kn_news_content',
    'kn_classify_labels',
    'kn_news_seo',
    'kn_news_label',
  ],
    // 添加以下配置跳过检查约束
  // skipCheckConstraints: true,
});