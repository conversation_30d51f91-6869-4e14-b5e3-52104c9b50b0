{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:multi": "tsx scripts/multi-db-migrate.ts", "db:test": "tsx db/multi-db-example.ts", "db:test-connection": "tsx -e \"import('./db/multi-db.js').then(m => m.dbManager.testConnection('core_news').then(r => console.log('连接测试:', r ? '成功' : '失败')))\""}, "dependencies": {"dotenv": "^17.2.1", "drizzle-orm": "^0.44.5", "mysql2": "^3.14.4", "nuxt": "^4.0.3", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"drizzle-kit": "^0.31.4", "tsx": "^4.20.5"}}