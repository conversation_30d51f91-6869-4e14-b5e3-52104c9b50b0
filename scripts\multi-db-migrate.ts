#!/usr/bin/env tsx

/**
 * 多数据库迁移脚本
 * 
 * 这个脚本用于管理多个数据库的迁移操作
 * 使用方法：
 * npm run db:generate -- --database=core_news
 * npm run db:migrate -- --database=core_news
 * npm run db:push -- --database=core_news
 */

import { execSync } from 'child_process';
import { writeFileSync, unlinkSync } from 'fs';
import { join } from 'path';
import { dbConfigs } from '../drizzle.config';
import { schemaMapping } from '../db/schemas';

// 支持的命令类型
type DrizzleCommand = 'generate' | 'migrate' | 'push' | 'pull' | 'check' | 'up';

/**
 * 多数据库迁移管理器
 */
class MultiDatabaseMigrator {
  
  /**
   * 创建临时配置文件
   * @param database 数据库名称
   * @returns 配置文件路径
   */
  private createTempConfig(database: keyof typeof dbConfigs): string {
    const config = dbConfigs[database];
    const tables = schemaMapping[database]?.tables || [];

    const configContent = `
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  out: './db/drizzle/${database}',
  schema: './db/schema.ts',
  dialect: 'mysql',
  dbCredentials: {
    host: "${config.host}",
    port: ${config.port},
    user: "${config.user}",
    password: "${config.password}",
    database: "${config.database}",
  },
  ${tables.length > 0 ? `tablesFilter: [${tables.map(t => `"${t}"`).join(', ')}],` : ''}
});
`;

    const tempConfigPath = `drizzle.config.${database}.ts`;

    writeFileSync(tempConfigPath, configContent);
    return tempConfigPath;
  }

  /**
   * 删除临时配置文件
   * @param configPath 配置文件路径
   */
  private deleteTempConfig(configPath: string): void {
    try {
      unlinkSync(configPath);
    } catch (error) {
      // 忽略删除错误
    }
  }

  /**
   * 执行指定数据库的 Drizzle 命令
   * @param command Drizzle 命令
   * @param database 数据库名称
   * @param extraArgs 额外参数
   */
  async executeCommand(
    command: DrizzleCommand,
    database: keyof typeof dbConfigs,
    extraArgs: string[] = []
  ): Promise<void> {

    if (!dbConfigs[database]) {
      throw new Error(`数据库配置 '${database}' 不存在`);
    }

    const config = dbConfigs[database];
    let tempConfigPath: string | null = null;

    try {
      // 创建临时配置文件
      tempConfigPath = this.createTempConfig(database);

      // 构建完整命令
      const fullCommand = `npx drizzle-kit ${command} --config=${tempConfigPath}`;

      console.log(`执行命令: ${fullCommand}`);
      console.log(`目标数据库: ${database} (${config.database})`);

      execSync(fullCommand, {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log(`✅ 数据库 '${database}' 的 ${command} 命令执行成功`);
    } catch (error) {
      console.error(`❌ 数据库 '${database}' 的 ${command} 命令执行失败:`, error);
      throw error;
    } finally {
      // 清理临时配置文件
      if (tempConfigPath) {
        this.deleteTempConfig(tempConfigPath);
      }
    }
  }

  /**
   * 为所有数据库执行命令
   * @param command Drizzle 命令
   * @param extraArgs 额外参数
   */
  async executeForAllDatabases(
    command: DrizzleCommand,
    extraArgs: string[] = []
  ): Promise<void> {
    const databases = Object.keys(dbConfigs) as (keyof typeof dbConfigs)[];
    
    console.log(`为所有数据库执行 ${command} 命令...`);
    
    for (const database of databases) {
      try {
        await this.executeCommand(command, database, extraArgs);
      } catch (error) {
        console.error(`数据库 '${database}' 执行失败，继续处理下一个数据库...`);
      }
    }
  }

  /**
   * 生成迁移文件
   * @param database 数据库名称（可选，不指定则为所有数据库生成）
   */
  async generate(database?: keyof typeof dbConfigs): Promise<void> {
    if (database) {
      await this.executeCommand('generate', database);
    } else {
      await this.executeForAllDatabases('generate');
    }
  }

  /**
   * 执行迁移
   * @param database 数据库名称（可选，不指定则为所有数据库执行）
   */
  async migrate(database?: keyof typeof dbConfigs): Promise<void> {
    if (database) {
      await this.executeCommand('migrate', database);
    } else {
      await this.executeForAllDatabases('migrate');
    }
  }

  /**
   * 推送 schema 到数据库
   * @param database 数据库名称（可选，不指定则推送到所有数据库）
   */
  async push(database?: keyof typeof dbConfigs): Promise<void> {
    if (database) {
      await this.executeCommand('push', database);
    } else {
      await this.executeForAllDatabases('push');
    }
  }

  /**
   * 从数据库拉取 schema
   * @param database 数据库名称
   */
  async pull(database: keyof typeof dbConfigs): Promise<void> {
    await this.executeCommand('pull', database);
  }

  /**
   * 检查 schema 差异
   * @param database 数据库名称（可选，不指定则检查所有数据库）
   */
  async check(database?: keyof typeof dbConfigs): Promise<void> {
    if (database) {
      await this.executeCommand('check', database);
    } else {
      await this.executeForAllDatabases('check');
    }
  }

  /**
   * 显示帮助信息
   */
  showHelp(): void {
    console.log(`
多数据库迁移工具使用说明：

命令格式：
  npm run db:multi <command> [options]

可用命令：
  generate [--database=<db_name>]  生成迁移文件
  migrate [--database=<db_name>]   执行迁移
  push [--database=<db_name>]      推送 schema 到数据库
  pull --database=<db_name>        从数据库拉取 schema
  check [--database=<db_name>]     检查 schema 差异

可用数据库：
${Object.keys(dbConfigs).map(db => `  - ${db}`).join('\n')}

示例：
  npm run db:multi generate --database=core_news
  npm run db:multi migrate --database=user_db
  npm run db:multi push  # 推送到所有数据库
  npm run db:multi check # 检查所有数据库
    `);
  }
}

// 命令行参数解析
function parseArgs(): { command?: DrizzleCommand; database?: string; help?: boolean } {
  const args = process.argv.slice(2);
  const result: { command?: DrizzleCommand; database?: string; help?: boolean } = {};

  for (const arg of args) {
    if (arg === '--help' || arg === '-h') {
      result.help = true;
    } else if (arg.startsWith('--database=')) {
      result.database = arg.split('=')[1];
    } else if (['generate', 'migrate', 'push', 'pull', 'check', 'up'].includes(arg)) {
      result.command = arg as DrizzleCommand;
    }
  }

  return result;
}

// 主函数
async function main() {
  const migrator = new MultiDatabaseMigrator();
  const { command, database, help } = parseArgs();

  if (help || !command) {
    migrator.showHelp();
    return;
  }

  try {
    switch (command) {
      case 'generate':
        await migrator.generate(database as keyof typeof dbConfigs);
        break;
      case 'migrate':
        await migrator.migrate(database as keyof typeof dbConfigs);
        break;
      case 'push':
        await migrator.push(database as keyof typeof dbConfigs);
        break;
      case 'pull':
        if (!database) {
          console.error('pull 命令需要指定数据库名称');
          process.exit(1);
        }
        await migrator.pull(database as keyof typeof dbConfigs);
        break;
      case 'check':
        await migrator.check(database as keyof typeof dbConfigs);
        break;
      default:
        console.error(`未知命令: ${command}`);
        migrator.showHelp();
        process.exit(1);
    }
  } catch (error) {
    console.error('执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (process.argv[1] && process.argv[1].includes('multi-db-migrate')) {
  main();
}

export { MultiDatabaseMigrator };
